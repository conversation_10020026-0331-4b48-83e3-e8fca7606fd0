/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer }) => {
    // Exclude better-sqlite3 from client-side bundle
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      };

      config.externals = config.externals || [];
      config.externals.push("better-sqlite3");
    }

    return config;
  },
  serverExternalPackages: ["better-sqlite3"],
};

module.exports = nextConfig;
