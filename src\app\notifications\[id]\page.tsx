"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AlertCircle, ArrowLeft } from "lucide-react";
import { useNotifications } from "@/contexts/notification-context";
import type { Notification } from "@/contexts/notification-context";
import { pdfToImage } from "@/lib/pdf-utils";

export default function NotificationDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { notifications, markAsRead } = useNotifications();
  const [notification, setNotification] = useState<Notification | null>(null);
  const [pdfImageUrl, setPdfImageUrl] = useState<string | null>(null);
  const [isLoadingPdfImage, setIsLoadingPdfImage] = useState(false);

  // Function to convert PDF to image
  const convertPdfToImage = async (pdfUrl: string) => {
    setIsLoadingPdfImage(true);
    try {
      let file: File;

      if (pdfUrl.startsWith("data:application/pdf;base64,")) {
        // Handle base64 PDF data
        const base64Data = pdfUrl.split(",")[1];
        const binaryString = atob(base64Data);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        const blob = new Blob([bytes], { type: "application/pdf" });
        file = new File([blob], "document.pdf", { type: "application/pdf" });
      } else {
        // Handle regular URL
        const response = await fetch(pdfUrl);
        const blob = await response.blob();
        file = new File([blob], "document.pdf", { type: "application/pdf" });
      }

      // Convert to image with higher scale for larger display
      const imageUrl = await pdfToImage(file, 2.5);
      setPdfImageUrl(imageUrl);
    } catch (error) {
      console.error("Error converting PDF to image:", error);
      setPdfImageUrl(null);
    } finally {
      setIsLoadingPdfImage(false);
    }
  };

  useEffect(() => {
    const notificationId = params.id as string;
    const foundNotification = notifications.find(
      (n) => n.id === notificationId
    );

    if (foundNotification) {
      setNotification(foundNotification);
      // Mark as read when viewing details
      if (!foundNotification.isRead) {
        markAsRead(foundNotification.id);
      }

      // Convert PDF to image if PDF URL exists
      if (foundNotification.pdfUrl) {
        convertPdfToImage(foundNotification.pdfUrl);
      }
    }
  }, [params.id, notifications, markAsRead]);

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  if (!notification) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              Notification Not Found
            </h3>
            <p className="text-muted-foreground mb-6">
              The notification you&apos;re looking for doesn&apos;t exist or has
              been removed.
            </p>
            <Button onClick={() => router.push("/")}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">{notification.title}</h1>
          <p className="text-muted-foreground">{notification.message}</p>
          {notification.pdfFileName && (
            <p className="text-sm text-muted-foreground mt-2">
              File: {notification.pdfFileName}
            </p>
          )}
        </div>

        {/* PDF Data Details */}
        {notification.pdfData && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>PDF Content Details</CardTitle>
              <CardDescription>
                Information extracted from the uploaded PDF file
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Template Information */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">
                    Template Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Template ID</Label>
                      <p className="text-sm text-muted-foreground font-mono">
                        {notification.pdfData.templateId}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">
                        Template Name
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        {notification.pdfData.templateName}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Layout Size</Label>
                      <p className="text-sm text-muted-foreground">
                        {notification.pdfData.layoutSize}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">
                        Generated At
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        {formatDate(notification.pdfData.generatedAt)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* User Data */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">User Data</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(notification.pdfData.userData).map(
                      ([key, value]) => (
                        <div key={key}>
                          <Label className="text-sm font-medium">{key}</Label>
                          <p className="text-sm text-muted-foreground">
                            {value || <span className="italic">Empty</span>}
                          </p>
                        </div>
                      )
                    )}
                  </div>
                </div>

                {/* Embedded Photo */}
                {notification.pdfData.photoBase64 && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3">
                      Embedded Photo
                    </h3>
                    <div className="max-w-xs">
                      <img
                        src={notification.pdfData.photoBase64}
                        alt="Embedded photo"
                        className="w-full h-auto border rounded-lg"
                      />
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* PDF Preview */}
        {notification.pdfUrl && (
          <Card>
            <CardHeader>
              <CardTitle>PDF Preview</CardTitle>
              <CardDescription>
                Preview of the uploaded PDF file as image
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="w-full border rounded-lg overflow-hidden bg-gray-50 min-h-[600px]">
                {isLoadingPdfImage ? (
                  <div className="flex items-center justify-center p-8">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                      <p className="text-muted-foreground text-sm">
                        Converting PDF to image...
                      </p>
                    </div>
                  </div>
                ) : pdfImageUrl ? (
                  <img
                    src={pdfImageUrl}
                    alt="PDF Preview"
                    className="w-full h-auto object-contain"
                  />
                ) : (
                  <div className="flex items-center justify-center p-8">
                    <p className="text-muted-foreground text-sm">
                      Failed to convert PDF to image
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
