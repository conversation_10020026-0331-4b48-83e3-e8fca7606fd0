// Server-side only imports
let fs: any;
let path: any;

if (typeof window === 'undefined') {
  fs = require('fs').promises;
  path = require('path');
}
// Removed unused imports - these functions are available but not currently used
// import {
//   extractPlaceholders,
//   updateImagePaths,
//   generateTemplateFilename
// } from './template-utils';
import type { Template } from '@/types/template';
import { TemplateModel } from './models/template';

const TEMPLATES_DIR = typeof window === 'undefined' ? path?.join(process.cwd(), 'public', 'templates') : '';

// Ensure templates directory exists
export async function ensureTemplatesDir() {
  try {
    await fs.access(TEMPLATES_DIR);
  } catch {
    await fs.mkdir(TEMPLATES_DIR, { recursive: true });
  }
}

// Load all templates - now uses SQLite only
export async function loadTemplates(): Promise<Template[]> {
  try {
    return await TemplateModel.findAll();
  } catch (error) {
    console.error('Error loading templates:', error);
    return [];
  }
}

// Save templates - now uses SQLite only (kept for backward compatibility)
export async function saveTemplates(templates: Template[]): Promise<void> {
  try {
    for (const template of templates) {
      const existing = await TemplateModel.findById(template.id);
      if (!existing) {
        await TemplateModel.create(template);
      } else {
        await TemplateModel.update(template.id, template);
      }
    }
  } catch (error) {
    console.error('Error saving templates to SQLite:', error);
    throw error;
  }
}

// Add a new template - now uses SQLite only
export async function addTemplate(template: Omit<Template, 'uploadedAt'>): Promise<void> {
  try {
    await TemplateModel.create(template);
  } catch (error) {
    console.error('Error adding template:', error);
    throw error;
  }
}

// Update an existing template - now uses SQLite only
export async function updateTemplate(id: string, updates: Partial<Template>): Promise<void> {
  try {
    await TemplateModel.update(id, updates);
  } catch (error) {
    console.error('Error updating template:', error);
    throw error;
  }
}

// Delete a template - now uses SQLite only
export async function deleteTemplate(id: string): Promise<void> {
  try {
    // Get template info before deletion
    const template = await TemplateModel.findById(id);

    // Delete from SQLite
    await TemplateModel.delete(id);

    // Delete the HTML file
    if (template) {
      try {
        await fs.unlink(path.join(TEMPLATES_DIR, template.filename));
      } catch (error) {
        console.error('Error deleting template file:', error);
      }

      // Delete the associated image folder if it exists
      const imageFolderPath = path.join(process.cwd(), 'public', id);
      try {
        await fs.access(imageFolderPath);
        // Folder exists, delete it recursively
        await fs.rm(imageFolderPath, { recursive: true, force: true });
        console.log(`Deleted image folder: ${imageFolderPath}`);
      } catch {
        // Folder doesn't exist or couldn't be deleted, which is fine
        console.log(`No image folder found for template ${id} or couldn't delete it`);
      }
    }
  } catch (error) {
    console.error('Error deleting template:', error);
    throw error;
  }
}



// Clean HTML content from mammoth.js DOCX conversion
export function cleanMammothHtml(htmlContent: string): string {
  let cleaned = htmlContent;

  // Mammoth.js produces cleaner HTML, but we still need some processing

  // Add proper DOCTYPE and HTML structure if missing
  if (!cleaned.includes('<!DOCTYPE')) {
    cleaned = '<!DOCTYPE html>\n<html>\n<head>\n<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">\n</head>\n<body style="word-wrap:break-word">\n' + cleaned + '\n</body>\n</html>';
  }

  // Add basic CSS for better formatting (similar to Word templates)
  const basicStyles = `
<style>
  body {
    font-size: 12pt;
    line-height: 1.5;
    margin: 1in;
    color: black;
  }
  p {
    margin: 0 0 10pt 0;
  }
  .center {
    text-align: center;
  }
  .justify {
    text-align: justify;
  }
  .bold {
    font-weight: bold;
  }
  .underline {
    text-decoration: underline;
  }
</style>`;

  // Insert basic styles after head tag
  if (cleaned.includes('<head>')) {
    cleaned = cleaned.replace('</head>', basicStyles + '\n</head>');
  }

  // Replace any Tanauan logo references with the official logo
  cleaned = cleaned.replace(
    /<img([^>]*?)alt="[^"]*tanauan[^"]*logo[^"]*"([^>]*?)>/gi,
    '<img$1alt="Tanauan Logo"$2src="/images/Tanauan_logo.png">'
  );

  // Clean up extra whitespace
  cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n');

  return cleaned.trim();
}

// Clean HTML content from Word-specific tags and attributes (minimal cleaning to preserve formatting)
export function cleanWordHtml(htmlContent: string): string {
  let cleaned = htmlContent;

  // Replace any stray replacement characters
  cleaned = cleaned.replace(/�+/g, ' ');

  // Ensure a DOCTYPE is present so browsers interpret the file correctly
  if (!/<!DOCTYPE/i.test(cleaned)) {
    cleaned = '<!DOCTYPE html>\n' + cleaned;
  }

  // Preserve everything else to keep the original layout intact
  // but still swap Tanauan logo references with the official logo
  cleaned = cleaned.replace(
    /<img([^>]*?)alt="[^"]*tanauan[^"]*logo[^"]*"([^>]*?)src="[^"]*"([^>]*?)>/gi,
    '<img$1alt="Tanauan Logo"$2src="/images/Tanauan_logo.png"$3>'
  );
  cleaned = cleaned.replace(
    /<img([^>]*?)src="[^"]*"([^>]*?)alt="[^"]*tanauan[^"]*logo[^"]*"([^>]*?)>/gi,
    '<img$1src="/images/Tanauan_logo.png"$2alt="Tanauan Logo"$3>'
  );


  return cleaned.trim();
}



// Save HTML content to a template file
export async function saveTemplateFile(filename: string, htmlContent: string): Promise<void> {
  await ensureTemplatesDir();
  const filePath = path.join(TEMPLATES_DIR, filename);
  await fs.writeFile(filePath, htmlContent, 'utf-8');
}

// Load HTML content from a template file
export async function loadTemplateFile(filename: string): Promise<string> {
  const filePath = path.join(TEMPLATES_DIR, filename);
  return await fs.readFile(filePath, 'utf-8');
}

// Reformat and clean an existing template file
export async function reformatTemplateFile(filename: string): Promise<void> {
  const filePath = path.join(TEMPLATES_DIR, filename);
  const originalContent = await fs.readFile(filePath, 'utf-8');
  const cleanedContent = cleanWordHtml(originalContent);
  await fs.writeFile(filePath, cleanedContent, 'utf-8');
}

// Replace placeholders in HTML content with actual values
export function replacePlaceholders(htmlContent: string, data: Record<string, string>): string {
  let processedContent = htmlContent;

  // Replace each placeholder with its corresponding value
  Object.entries(data).forEach(([key, value]) => {
    // Normalize the key and allow any whitespace (spaces or newlines)
    const parts = key.trim().split(/\s+/).map((part) =>
      part.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    );
    const pattern = parts.join('\\s+');
    const regex = new RegExp(`\\[\\s*${pattern}\\s*\\]`, 'gi');
    processedContent = processedContent.replace(regex, value || '');
  });

  return processedContent;
}


// Generate PDF from HTML content with specified layout size
export function generatePDFStyles(layoutSize: 'A4' | 'Letter'): string {
  const pageStyles = layoutSize === 'A4'
    ? {
        width: '8.27in',
        height: '11.69in'
      }
    : {
        width: '8.5in',
        height: '11in'
      };

  return `
    <style>
      @page {
        size: ${layoutSize};
        margin: 0;
      }

      @media print {
        body {
          width: ${pageStyles.width};
          height: ${pageStyles.height};
          margin: 0;
          padding: 0;
        }
      }

      /* Body acts as white A4/Letter paper with padding as margins */
      body {
        margin: 0;
        padding: 1in; /* This creates the margins on the white paper */
        background: white;
        width: ${pageStyles.width};
        height: ${pageStyles.height};
        box-sizing: border-box;
        overflow: hidden;
      }

      /* WordSection1 (aqua area) - no margins/padding, just position relative */
      .WordSection1 {
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        box-sizing: border-box !important;
        position: relative !important; /* This contains absolute positioned elements */
      }

      /* Ensure absolute positioned elements stay within the aqua container */
      .WordSection1 * {
        max-width: 100% !important;
      }

      /* Handle images to fit within the aqua container */
      .WordSection1 img {
        max-width: 100% !important;
        height: auto !important;
        object-fit: contain;
      }

      /* Ensure text and other content doesn't overflow */
      .WordSection1 p, .WordSection1 div, .WordSection1 span {
        word-wrap: break-word;
        overflow-wrap: break-word;
      }
    </style>
  `;
}
