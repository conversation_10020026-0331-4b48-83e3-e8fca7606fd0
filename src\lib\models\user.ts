import { getDatabase } from '../database';

// Server-side only imports
let CryptoJS: any;
if (typeof window === 'undefined') {
  CryptoJS = require('crypto-js');
}

// Secret key for encryption (in production, this should be from environment variables)
const SECRET_KEY = 'LDIS_AUTH_SECRET_2024';

export interface User {
  id?: number;
  username: string;
  passwordHash: string;
  recoveryOptions: {
    privateKey?: string;
    securityQuestions?: {
      question: string;
      answerHash: string;
    }[];
  };
  createdAt?: string;
  updatedAt?: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface SignupData {
  username: string;
  password: string;
  recoveryMethod: 'privateKey' | 'securityQuestions';
  privateKey?: string;
  securityQuestions?: {
    question: string;
    answer: string;
  }[];
}

export interface RecoveryData {
  username: string;
  recoveryMethod: 'privateKey' | 'securityQuestions';
  privateKey?: string;
  securityAnswers?: string[];
}

// Security questions
export const SECURITY_QUESTIONS = [
  "What was the name of your childhood best friend?",
  "What is the name of the street you grew up on?",
  "What was the name of your first pet?",
  "What was your favorite subject in school?",
  "What is the middle name of your oldest sibling?"
];

// Encryption utilities
export const encrypt = (text: string): string => {
  return CryptoJS.AES.encrypt(text, SECRET_KEY).toString();
};

export const decrypt = (ciphertext: string): string => {
  const bytes = CryptoJS.AES.decrypt(ciphertext, SECRET_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
};

// Hash password
export const hashPassword = (password: string): string => {
  return CryptoJS.SHA256(password + SECRET_KEY).toString();
};

// Generate private key
export const generatePrivateKey = (): string => {
  return CryptoJS.lib.WordArray.random(32).toString();
};

// User database operations
export class UserModel {
  static async findByUsername(username: string): Promise<User | null> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const stmt = db.prepare('SELECT * FROM users WHERE username = ?');
    const row = stmt.get(username) as any;
    
    if (!row) return null;
    
    return {
      id: row.id,
      username: row.username,
      passwordHash: row.password_hash,
      recoveryOptions: JSON.parse(row.recovery_options || '{}'),
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  static async create(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const stmt = db.prepare(`
      INSERT INTO users (username, password_hash, recovery_options)
      VALUES (?, ?, ?)
    `);
    
    const result = stmt.run(
      userData.username,
      userData.passwordHash,
      JSON.stringify(userData.recoveryOptions)
    );
    
    return {
      id: result.lastInsertRowid as number,
      ...userData
    };
  }

  static async updateRecoveryOptions(username: string, recoveryOptions: User['recoveryOptions']): Promise<boolean> {
    const db = await getDatabase();
    const stmt = db.prepare(`
      UPDATE users 
      SET recovery_options = ?, updated_at = CURRENT_TIMESTAMP
      WHERE username = ?
    `);
    
    const result = stmt.run(JSON.stringify(recoveryOptions), username);
    return result.changes > 0;
  }

  static async updatePassword(username: string, newPasswordHash: string): Promise<boolean> {
    const db = await getDatabase();
    const stmt = db.prepare(`
      UPDATE users 
      SET password_hash = ?, updated_at = CURRENT_TIMESTAMP
      WHERE username = ?
    `);
    
    const result = stmt.run(newPasswordHash, username);
    return result.changes > 0;
  }

  static async getAllUsers(): Promise<User[]> {
    const db = await getDatabase();
    const stmt = db.prepare('SELECT * FROM users ORDER BY created_at DESC');
    const rows = stmt.all() as any[];
    
    return rows.map(row => ({
      id: row.id,
      username: row.username,
      passwordHash: row.password_hash,
      recoveryOptions: JSON.parse(row.recovery_options || '{}'),
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }));
  }

  static async deleteUser(username: string): Promise<boolean> {
    const db = await getDatabase();
    const stmt = db.prepare('DELETE FROM users WHERE username = ?');
    const result = stmt.run(username);
    return result.changes > 0;
  }
}

// Authentication functions using the new model
export const signup = async (signupData: SignupData): Promise<{ success: boolean; message: string; privateKey?: string }> => {
  if (typeof window !== 'undefined') {
    throw new Error('Authentication operations can only be performed on the server side');
  }

  try {
    // Check if username already exists
    const existingUser = await UserModel.findByUsername(signupData.username);
    if (existingUser) {
      return { success: false, message: 'Username already exists' };
    }

    // Create recovery options
    const recoveryOptions: User['recoveryOptions'] = {};
    let privateKey: string | undefined;

    if (signupData.recoveryMethod === 'privateKey') {
      privateKey = signupData.privateKey || generatePrivateKey();
      recoveryOptions.privateKey = encrypt(privateKey);
    } else if (signupData.recoveryMethod === 'securityQuestions' && signupData.securityQuestions) {
      recoveryOptions.securityQuestions = signupData.securityQuestions.map(sq => ({
        question: sq.question,
        answerHash: hashPassword(sq.answer.toLowerCase().trim())
      }));
    }

    // Create new user
    await UserModel.create({
      username: signupData.username,
      passwordHash: hashPassword(signupData.password),
      recoveryOptions
    });

    return { 
      success: true, 
      message: 'Account created successfully',
      privateKey: signupData.recoveryMethod === 'privateKey' ? privateKey : undefined
    };
  } catch (error) {
    console.error('Signup error:', error);
    return { success: false, message: 'Failed to create account' };
  }
};

export const login = async (credentials: LoginCredentials): Promise<{ success: boolean; message: string }> => {
  if (typeof window !== 'undefined') {
    throw new Error('Authentication operations can only be performed on the server side');
  }

  try {
    const user = await UserModel.findByUsername(credentials.username);
    if (!user) {
      return { success: false, message: 'Invalid username or password' };
    }

    const passwordHash = hashPassword(credentials.password);
    if (user.passwordHash !== passwordHash) {
      return { success: false, message: 'Invalid username or password' };
    }

    return { success: true, message: 'Login successful' };
  } catch (error) {
    console.error('Login error:', error);
    return { success: false, message: 'Login failed' };
  }
};

export const recoverAccount = async (recoveryData: RecoveryData): Promise<{ success: boolean; message: string; newPassword?: string }> => {
  try {
    const user = await UserModel.findByUsername(recoveryData.username);
    if (!user) {
      return { success: false, message: 'User not found' };
    }

    if (recoveryData.recoveryMethod === 'privateKey') {
      if (!recoveryData.privateKey || !user.recoveryOptions.privateKey) {
        return { success: false, message: 'Invalid recovery method' };
      }

      const storedPrivateKey = decrypt(user.recoveryOptions.privateKey);
      if (storedPrivateKey !== recoveryData.privateKey) {
        return { success: false, message: 'Invalid private key' };
      }
    } else if (recoveryData.recoveryMethod === 'securityQuestions') {
      if (!recoveryData.securityAnswers || !user.recoveryOptions.securityQuestions) {
        return { success: false, message: 'Invalid recovery method' };
      }

      const storedQuestions = user.recoveryOptions.securityQuestions;
      if (storedQuestions.length !== recoveryData.securityAnswers.length) {
        return { success: false, message: 'Invalid number of answers' };
      }

      for (let i = 0; i < storedQuestions.length; i++) {
        const answerHash = hashPassword(recoveryData.securityAnswers[i].toLowerCase().trim());
        if (answerHash !== storedQuestions[i].answerHash) {
          return { success: false, message: 'Incorrect security answers' };
        }
      }
    }

    // Generate new password
    const newPassword = CryptoJS.lib.WordArray.random(8).toString().substring(0, 12);
    const newPasswordHash = hashPassword(newPassword);

    // Update password
    await UserModel.updatePassword(recoveryData.username, newPasswordHash);

    return { 
      success: true, 
      message: 'Account recovered successfully',
      newPassword
    };
  } catch (error) {
    console.error('Recovery error:', error);
    return { success: false, message: 'Account recovery failed' };
  }
};
