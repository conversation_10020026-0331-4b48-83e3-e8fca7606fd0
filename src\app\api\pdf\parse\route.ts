import { NextRequest, NextResponse } from 'next/server';

// For server-side PDF processing, we'll return the PDF data and let the client handle image conversion

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'PDF file is required' },
        { status: 400 }
      );
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Convert buffer to string to search for embedded data
    const pdfText = buffer.toString('latin1');

    // Look for the embedded data markers
    const startMarker = 'LDIS_DATA_BEGIN:';
    const endMarker = ':LDIS_DATA_END';

    const startIndex = pdfText.indexOf(startMarker);
    const endIndex = pdfText.indexOf(endMarker);

    let parsedData = null;
    let hasEmbeddedData = false;

    if (startIndex !== -1 && endIndex !== -1) {
      // Extract the JSON data
      const jsonStart = startIndex + startMarker.length;
      const jsonData = pdfText.substring(jsonStart, endIndex);

      try {
        parsedData = JSON.parse(jsonData);
        hasEmbeddedData = true;
      } catch (parseError) {
        console.error('Error parsing embedded JSON:', parseError);
      }
    }

    // Convert PDF to base64 for client-side processing
    const pdfBase64 = buffer.toString('base64');

    return NextResponse.json({
      success: true,
      hasEmbeddedData,
      data: parsedData,
      pdfBase64: `data:application/pdf;base64,${pdfBase64}`
    });

  } catch (error) {
    console.error('Error parsing PDF:', error);
    return NextResponse.json(
      { error: 'Failed to parse PDF file' },
      { status: 500 }
    );
  }
}
